<template>
  <el-popover placement="right" :width="200" :trigger="handleActive(data) ? 'hover' : 'click'" :visible="data[`_popover${type}Visible`]">
    <template #reference>
      <svg-icon
        @click="handlePopVisible(data)"
        @mouseenter="events.iconMouseenter(data)"
        @mouseleave="events.iconMouseleave(data)"
        :name="handleActive(data) ? 'icon-zan-active' : 'icon-zan'"
        width="18px"
        :class="{
          active: handleActive(data),
          'cai-btn': type == 'cai',
          'zan-btn': type == 'zan',
        }"
      />
    </template>
    <div class="pingjia">
      <div>
        <el-input v-if="!handleActive(data)" v-model="data.markResult.remark" style="width: 100%; margin: 3px" :rows="2" type="textarea" placeholder="可填写详情备注" />
        <b v-else>{{ data.markResult.remark }}</b>
      </div>
    </div>
    <div style="text-align: right; margin: 0" v-if="!handleActive(data)">
      <el-button size="small" text @click="handlePopClose(data, type)">取消</el-button>
      <el-button size="small" type="primary" @click="handleConfirm(data, type == 'zan' ? '0' : '1')"> 确定 </el-button>
    </div>
  </el-popover>
</template>

<script setup>
import { ref, reactive, onMounted, defineProps, defineEmits, computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import * as markApi from "@/api/eval-mark";
import useCtx from "@/hooks/useCtx";
const { $router, proxy, $app } = useCtx();

// 定义emit事件
const emit = defineEmits(['onMarkSaved']);

const props = defineProps({
  data: { type: Object },
  index: { type: Number },
  type: { type: String },
  resultData: { type: Object },
  mode: { type: String },
  resultData: { type: Object },
  markRecordId: { type: String },
  strategyId: { type: String },
  targetId: { type: String },
  texts: { type: Array },
});
const handleActive = (data) => {
  return (props.type == "zan" && data?.markResult?.value === "0") || (props.type == "cai" && data?.markResult?.value === "1");
};
const handleHover = (data) => {
  if (handleActive(data)) {
    data;
  }
};
// 打开备注弹窗
const handlePopVisible = (data) => {
  // console.log("handlePopVisible ", data);

  if (handleActive(data)) {
    data[`_popover${props.type}Visible`] = false;
    data.markResult.type = "";
    data.markResult.remark = "";
    handleConfirm(data, "");
  } else {
    data[`_popover${props.type}Visible`] = true;
    data[`_popover${props.type == "zan" ? "cai" : "zan"}Visible`] = false;
  }
};
// 关闭弹窗
const handlePopClose = (data) => {
  data[`_popover${props.type}Visible`] = false;
};
const handleRemark = (data) => {
  return data.markResult.remark;
};

/**
 *
 */
const getParams = (item, type) => {
  const params = {
    compIdx: item.columnIndex,
    docIdx: item.rowIndex,
    remark: item.markResult.remark,
    strategyId: props.strategyId,
    markRecordId: props.markRecordId,
    title: item.title,
    url: item.url,
    docId: item.id,
    value: type,
    isMock: item.mock,
    resultId: item.markResult?.resultId,
  };
  return params;
};

function getRequest(item, value) {
  const markResultTraceDTO = getParams(item, value);
  const request = {
    id: props.markRecordId,
    targets: [
      {
        targetId: props.targetId,
        traceList: dataC.isEmpty(markResultTraceDTO) ? [] : [markResultTraceDTO],
      },
    ],
  };
  console.log("request", request);
  return request;
}

// 点赞确定
const handleConfirm = (item, value) => {
  console.log("value", item, value);
  console.log("props", props);

  if (dataC.isEmpty(value)) {
    // 取消 调用删除接口
    console.log("删除resultId:", item.markResult.resultId);

    markApi.deleteMarkResult(item.markResult.resultId).then((res) => {
      $app.$message.success("取消成功");
      item.markResult.value = "";
      // 触发回调，传递docId和组件顺序
      emit('onMarkSaved', item.id, item.columnIndex);
    });
  } else if (value === "0" || value === "1") {
    const request = getRequest(item, value);
    markApi.doSave(request).then((res) => {
      item.markResult.value = value;
      $app.$message.success(value == "0" ? "点赞成功" : value == "1" ? "点踩成功" : "取消成功");
      item[`_popover${props.type}Visible`] = false;
      item.remark = handleRemark(item);
      // 触发回调，传递docId和组件顺序
      emit('onMarkSaved', item.id, item.columnIndex);
    });
  }
};
const events = reactive({
  iconMouseenter: (data) => {
    if (handleActive(data)) {
      data[`_popover${props.type}Visible`] = true;
    }
  },
  iconMouseleave: (data) => {
    if (handleActive(data)) {
      data[`_popover${props.type}Visible`] = false;
    }
  },
});
</script>

<style lang="scss" scoped>
.zan-btn,
.cai-btn {
  vertical-align: middle; /* SVG图标对齐 */
  margin-left: 28px;
  cursor: pointer;
}
.cai-btn {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}
</style>