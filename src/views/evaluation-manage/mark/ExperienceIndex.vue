<template>
  <page-wrapper :route-name="`experience-index::`">
    <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="allChainVisible = false" :icon="icons.Back">返回</el-button><Strong></Strong> </template>
      <ExperiencePosition ref="experiencePositionRef"></ExperiencePosition>
    </el-dialog>

    <div class="experience-index">
      <el-row style="padding-bottom: 8px;">
        <el-col :span="24">
          <el-row>
            <el-col :span="3">
              <div>
                <el-select v-model="modelValue.region" placeholder="请选择区域" @change="events.regionChange" style="width: 140px">
                  <el-option v-for="item in modelValue.regionList" :label="item.name" :value="item.code" />
                </el-select></div
            ></el-col>
            <el-col :span="19">
              <div style="display: flex; flex-wrap: wrap; gap: 10px">
                <div v-for="item in modelValue.flowList">
                  <el-link
                    :type="modelValue.flowCode == item.code ? `primary` : `info`"
                    :icon="icons[item.icon]"
                    :underline="false"
                    @click="events.flowCodeChange(item)"
                    style="font-size: 15px; font-weight: bold"
                    >{{ item.name }}</el-link
                  >
                </div>

                <div v-if="modelValue.flowCode == 'other'" class="flexBetween">
                  <el-input
                    v-model="modelValue.flowInput"
                    placeholder="请输入其他产品流程id"
                    @blur="events.flowInputBlur"
                    style="padding-right: 4px"
                  ></el-input>
                  <el-button :icon="Plus" circle type="primary" @click="events.addOtherProcess"></el-button>
                </div>
              </div>
            </el-col>
            <el-col :span="2">
              <div style="margin-left: auto; display: flex; flex-direction: row-reverse;">
                <el-dropdown trigger="click">
                  <el-button type="primary">更多操作</el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :icon="icons.Open" @click="modelValue.displayScore = true" :disabled="modelValue.displayScore == true">
                        显示策略数据
                      </el-dropdown-item>
                      <el-dropdown-item :icon="icons.TurnOff" @click="modelValue.displayScore = false" :disabled="modelValue.displayScore == false">
                        隐藏策略数据
                      </el-dropdown-item>
                      <el-dropdown-item :icon="icons.ArrowDownBold" @click="events.unfoldContent" :disabled="dataC.isEmpty(modelValue.recall)">
                        展开content
                      </el-dropdown-item>
                      <el-dropdown-item :icon="icons.ArrowUpBold" @click="events.foldContent" :disabled="dataC.isEmpty(modelValue.recall)">
                        折叠content
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div></el-col
            >
          </el-row>

          <div class="first flex"></div>

          <div class="search-row">
            <el-input v-model="modelValue.search" placeholder="请输入你要测评的query" :prefix-icon="icons.Search" @keydown.enter="events.doExperience">
              <template #append>
                <el-button @click="events.doExperience">搜索</el-button>
              </template>
            </el-input>
          </div>

          <div class="flexBetween">
            <ProcessBox
              label="A"
              :region="modelValue.processA.region"
              :region-list="modelValue.regionList"
              :markRecordId="modelValue.markRecordId"
              :processName="modelValue.processA.name"
              :isSelected="modelValue.selectedProcess === 'A'"
              :isEditInputArgs="modelValue.processA.isEditInputArgs"
              :hasValue="!dataC.isEmpty(modelValue.processA.processId)"
              @select="events.selectProcess('A')"
              @clear="events.clearProcess('A')"
              @editParams="events.editInputArgs('A')"
            />

            <div class="second">
              <FlowParam
                v-if="modelValue.processA.isEditInputArgs || modelValue.processB.isEditInputArgs"
                :key="modelValue.paramUseKey"
                :inputArgs="modelValue.processA.isEditInputArgs ? modelValue.processA.inputArgs : modelValue.processB.inputArgs"
                style="width: 100%"
              ></FlowParam>
            </div>

            <ProcessBox
              label="B"
              :region="modelValue.processB.region"
              :region-list="modelValue.regionList"
              :markRecordId="modelValue.markRecordId"
              :processName="modelValue.processB.name"
              :isSelected="modelValue.selectedProcess === 'B'"
              :isEditInputArgs="modelValue.processB.isEditInputArgs"
              :hasValue="!dataC.isEmpty(modelValue.processB.processId)"
              @select="events.selectProcess('B')"
              @clear="events.clearProcess('B')"
              @editParams="events.editInputArgs('B')"
            />
          </div>
        </el-col>
      </el-row>

      <el-row style="flex-grow: 1; overflow: hidden;" >
        <el-tabs v-model="modelValue.activeName" @tab-change="events.tabChange" style="width: 100%">
          <el-tab-pane label="搜索结果" name="searchPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="searchPane" />
            <el-row v-if="!dataC.isEmpty(modelValue.searchResult)" :gutter="8" class="searchPane">
              <template v-for="(strategy, strategyIdx) in modelValue.searchResult">
                <el-col :span="multipleStrategy ? 12 : 24">
                  <div class="mark-pane">
                    <div v-if="strategy.traceId">
                      <span style="font-weight: bold; font-size: 12px">traceId：</span><span>{{ strategy.traceId }}&nbsp;&nbsp;</span>
                      <el-icon class="icon-copy" @click="copyText(strategy.traceId)">
                        <CopyDocument />
                      </el-icon>
                    </div>
                    <MarkItem
                      v-for="(doc, docIdx) in strategy.recallList"
                      :key="`markItemRef-${strategy.strategyId}-${docIdx}`"
                      v-model="strategy.recallList[docIdx].markResult"
                      :ref="`markItemRef-${strategy.strategyId}-${docIdx}`"
                      :dimsInfo="[]"
                      :docIndex="docIdx"
                      :recallInfo="doc"
                      :displayScore="modelValue.displayScore"
                      :markRecordId="modelValue.markRecordId"
                      :targetId="strategy.targetId"
                      :tags="strategyIdx == 0 ? 'A' : 'B'"
                    ></MarkItem>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Chat效果" name="chatPane">
            <my-empty v-if="dataC.isEmpty(modelValue.searchResult)" :size="120" class="chatPane" />
            <el-row v-if="!dataC.isEmpty(modelValue.searchResult)" :gutter="8" class="chatPane">
              <template v-for="(strategy, strategyIdx) in modelValue.searchResult">
                <el-col :span="multipleStrategy ? 12 : 24">
                  <div class="flex">
                    <strong style="margin-right: 5px; color: red">结果：</strong>
                  </div>
                  <div class="chat-pane">
                    <MarkChat :chat="strategy.chat || {}" :recallInfo="strategy.recallList" @reloadChat="events.reloadChat(strategy.metadata.tag)"></MarkChat>
                  </div>
                </el-col>
              </template>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-row>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, nextTick } from "vue";
import * as icons from "@element-plus/icons-vue";
import { keys, cloneDeep, result } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import MarkItem from "./MarkItem.vue";
import MarkChat from "./MarkChat.vue";
import FlowParam from "./FlowParam.vue";
import ExperiencePosition from "./ExperiencePosition.vue";
import * as markApi from "@/api/eval-mark";
import * as sceneApi from "@/api/scene";
import * as commonApi from "@/api/common";
import { copyText, getText } from "@/utils/helpers";
import { Plus, Close } from "@element-plus/icons-vue";
import ProcessBox from "./ProcessBox.vue";
import * as evalSettingApi from "@/api/eval-setting";

interface Process {
  name: string;
  processId: string;
  region: string;
  inputArgs: Array<{ key: string; value: any }>;
  isEditInputArgs: boolean;
  metadata: {
    tag: string;
  };
}

interface Scene {
  name: string;
  processId: string;
  version: string;
}

interface Region {
  name: string;
  code: string;
}

interface FlowItem {
  icon: string;
  name: string;
  code: string;
  sort?: number;
}

interface SearchResult {
  traceId?: string;
  chat?: {
    traceId: string;
    [key: string]: any;
  };
  recallList: Array<any>;
  metadata: {
    tag: string;
  };
  [key: string]: any;
}

interface Chat {
  id: string;
  traceId: string;
  [key: string]: any;
}

interface ModelValue {
  traceId: string;
  flowCode: string;
  flowInput: string;
  flowList: FlowItem[];
  region: string;
  regionList: Region[];
  searchResult: SearchResult[];
  activeName: string;
  paramUseKey: number;
  inputArgs: Array<{ key: string; value: any }>;
  search: string;
  markRecordId: string;
  selectedProcessList: string[];
  displayScore: boolean;
  selectedProcess: string;
  sceneList: Scene[];
  processA: Process;
  processB: Process;
  chat?: Chat;
  lastSend: Number;
}

const { $app, $router, $auth, proxy } = useCtx();
const { api } = useStore();

const iconType = ["ChatSquare", "ChatRound", "ChatLineRound"];

const modelValue = reactive<ModelValue>({
  traceId: "",
  flowCode: "",
  flowInput: "",
  flowList: [],
  region: "hf",
  regionList: [],
  searchResult: [],
  activeName: "searchPane",
  paramUseKey: 0,
  inputArgs: [],
  search: "",
  markRecordId: "",
  selectedProcessList: [],
  displayScore: false,
  selectedProcess: "A",
  sceneList: [],
  processA: {
    name: "",
    processId: "",
    inputArgs: [],
    isEditInputArgs: false,
    metadata: {
      tag: "A",
    },
  },
  processB: {
    name: "",
    processId: "",
    inputArgs: [],
    isEditInputArgs: false,
    metadata: {
      tag: "B",
    },
  },
  lastSend: Date.now(),
});

const multipleStrategy = computed(() => {
  return !dataC.isEmpty(modelValue.processA.processId) && !dataC.isEmpty(modelValue.processB.processId);
});

//全链路显示
const allChainVisible = ref(false);

//获取全链路跳转的query参数
const getAllChainToQuery = () => {
  return {
    mode: "ceping",
    chatId: modelValue.chat?.id || "",
  };
};

//获取全链路跳转url地址
const allChainTo = computed(() => {
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  (Object.keys(query) as Array<keyof typeof query>).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/experience-index/position?${routerQuery.toString()}`;
});

const events = reactive({
  //搜索和chat面板切换时
  tabChange: (_tabPaneName: string) => {
    // 面板切换逻辑
  },
  //全链路
  allChain: async (e?: Event) => {
    //阻止原生路由跳转事件
    if (e) {
      e.preventDefault();
    }
    //修改当前路由
    await $router.push({
      name: `experience-index`,
      query: getAllChainToQuery(),
    });
    //打开一个全屏dialog
    allChainVisible.value = true;
    //刷新全链路
    nextTick(() => {
      proxy.$refs.experiencePositionRef.getTraceinfo();
    });
  },
  //展开content
  unfoldContent: () => {
    modelValue.searchResult.forEach((strategy: SearchResult) => {
      strategy.recallList.forEach((_, docIdx: number) => {
        proxy.$refs[`markItemRef-${strategy.metadata.tag}-${docIdx}`][0].unfoldContent();
      });
    });
  },
  //折叠content
  foldContent: () => {
    modelValue.searchResult.forEach((strategy: SearchResult) => {
      strategy.recallList.forEach((_, docIdx: number) => {
        proxy.$refs[`markItemRef-${strategy.metadata.tag}-${docIdx}`][0].foldContent();
      });
    });
  },
  //切换为标注模式
  changeMode: () => {
    $router.push({
      name: `mark-index`,
    });
  },

  //修改流程入参
  editInputArgs: (item: string) => {
    console.log("editInputArgs", item);

    if (item === "A") {
      modelValue.processB.isEditInputArgs = false;
      modelValue.processA.isEditInputArgs = !modelValue.processA.isEditInputArgs;
      return;
    }

    if (item === "B") {
      modelValue.processA.isEditInputArgs = false;
      modelValue.processB.isEditInputArgs = !modelValue.processB.isEditInputArgs;
      return;
    }
  },

  //获取流程动态表单参数
  getInputArgs: async (processId: string): Promise<Array<{ key: string; value: any }>> => {
    try {
      const result = await commonApi.getProcessInputArgs(processId);
      if (!result?.inputArgs) {
        $app.$message.warning("未获取到流程参数，接口返回格式异常");
        return [];
      }
      const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");
      modelValue.paramUseKey += 1;
      return resp;
    } catch (error) {
      console.error("Failed to get input args:", error);
      $app.$message.warning("获取流程参数失败");
      return [];
    }
  },
  flowCodeChange: async (item: any) => {
    modelValue.flowCode = item.code;
    modelValue.flowInput = "";
    modelValue.inputArgs = [];

    if (item.code === "other") {
      return;
    }
    if (modelValue.selectedProcess === "A") {
      modelValue.processA.processId = item.code;
      await updateProcessA();
    }
    if (modelValue.selectedProcess === "B") {
      modelValue.processB.processId = item.code;
      await updateProcessB();
    }
  },
  flowInputBlur: () => {},

  regionChange: () =>{

    if(dataC.isEmpty(modelValue.selectedProcess)){
      return;
    }

    const currentProcess = modelValue.selectedProcess === "A" ? modelValue.processA : modelValue.processB;
    currentProcess.region = modelValue.region;
  },

  addOtherProcess: async () => {
    if (dataC.isEmpty(modelValue.flowInput)) {
      $app.$message.warning("请输入流程id");
      return;
    }
    //检验合法性
    const isValidProcess = modelValue.sceneList.some((scene) => scene.processId === modelValue.flowInput);
    if (!isValidProcess) {
      $app.$message.warning("请输入有效的流程id");
      return;
    }

    if (modelValue.selectedProcess === "A") {
      modelValue.processA.processId = modelValue.flowInput;
      await updateProcessA();
    }
    if (modelValue.selectedProcess === "B") {
      modelValue.processB.processId = modelValue.flowInput;
      await updateProcessB();
    }

    modelValue.flowInput = "";
  },

  //获取chat验证结
  reloadChat: async (tagName: string) => {
    try {
      const result = await markApi.getMarkReocordById(modelValue.markRecordId, false, false, true);

      if (!dataC.isEmpty(modelValue.processA.processId)) {
        const response = result.data.targets.find((item: SearchResult) => item.metadata.tag === tagName);
        const searchResult = modelValue.searchResult.find((item: SearchResult) => item.metadata.tag === tagName);
        if (response && searchResult) {
          searchResult.chat = response.chat;
        }
      }
    } catch (error) {
      console.error("Failed to reload chat:", error);
      $app.$message.error("重新加载聊天失败");
    }
  },

  //单query搜索
  doExperience: async () => {

    //防止页面频繁点击
    if(isQuickClick()){
      //频繁操作 
      return;
    }


    try {
      // 清空当前页面信息
      modelValue.traceId = "";
      modelValue.searchResult = [];

      if (dataC.isEmpty(modelValue.search)) {
        $app.$message.warning("请输入搜索内容");
        return;
      }

      // 构建流程列表
      const processList = [];

      if (!dataC.isEmpty(modelValue.processA.processId)) {
        processList.push({
          processId: modelValue.processA.processId,
          regionCode: modelValue.processA.region,
          payload: buildPayload(modelValue.processA.inputArgs),
          metadata: modelValue.processA.metadata,
        });
      }

      if (!dataC.isEmpty(modelValue.processB.processId)) {
        processList.push({
          processId: modelValue.processB.processId,
          regionCode: modelValue.processB.region,
          payload: buildPayload(modelValue.processB.inputArgs),
          metadata: modelValue.processB.metadata,
        });
      }

      if (processList.length === 0) {
        $app.$message.warning("请至少选择一个策略");
        return;
      }

      const data = {
        query: modelValue.search,
        processList: processList,
      };

      console.log("create markRecord data:", data);

      const result = await markApi.createMarkRecord(data);

      if (!result.data) {
        $app.$message.error("创建测评记录失败");
        return;
      }

      modelValue.markRecordId = result.data;
      await events.findByMarkRecordId(result.data);
    } catch (error) {
      console.error("搜索失败:", error);
      $app.$message.error("搜索失败，请稍后重试");
    }
  },

  findByMarkRecordId: async (markRecordId: string) => {
    await markApi.getMarkReocordById(markRecordId, true, false, true).then((result) => {
      events.render(result);
    });
  },

  // 将获取到的测评记录渲染到页面
  render: (result: any) => {
    modelValue.markRecordId = result.data.id;
    modelValue.search = result.data.query;
    //判断搜索结果是否为空
    const emptyFlag = result.data.targets.every((item: SearchResult) => dataC.isEmpty(item.recallList));
    if (emptyFlag) {
      $app.$message.warning("搜索结果为空!");
      return;
    }

    if (!dataC.isEmpty(modelValue.processA.processId)) {
      const processResult = result.data.targets.find((item: SearchResult) => item.metadata.tag === "A");
      console.log("processResult:", processResult);
      if (processResult) {
        modelValue.searchResult.push(processResult);
      }
    }

    if (!dataC.isEmpty(modelValue.processB.processId)) {
      const processResult = result.data.targets.find((item: SearchResult) => item.metadata.tag === "B");
      if (processResult) {
        modelValue.searchResult.push(processResult);
      }
    }
  },

  selectProcess: (process: string) => {
    if (process == modelValue.selectedProcess) {
      modelValue.selectedProcess = "";
    } else {
      modelValue.selectedProcess = process;
    }
  },
  clearProcess: async (process: string) => {
    console.log("clear:", process);

    if (process === "A") {
      modelValue.processA.processId = "";
      await updateProcessA();
    } else if (process === "B") {
      modelValue.processB.processId = "";
      await updateProcessB();
    }

    // 从搜索结果中移除指定标签的流程结果
    modelValue.searchResult = modelValue.searchResult.filter((item: SearchResult) => item.metadata.tag !== process);
    modelValue.inputArgs = [];
  },
});

// 修改 getInputArgs 函数
const getInputArgs = async (processId: string): Promise<Array<{ key: string; value: any }>> => {
  try {
    const result = await commonApi.getProcessInputArgs(processId);
    if (!result?.inputArgs) {
      $app.$message.warning("未获取到流程参数，接口返回格式异常");
      return [];
    }
    const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");
    modelValue.paramUseKey += 1;
    return resp;
  } catch (error) {
    console.error("Failed to get input args:", error);
    $app.$message.warning("获取流程参数失败");
    return [];
  }
};

// 优化 updateProcess 函数
const updateProcess = async (process: "A" | "B") => {
  const currentProcess = process === "A" ? modelValue.processA : modelValue.processB;
  const targetScene = modelValue.sceneList.find((item: Scene) => item.processId === currentProcess.processId);

  if (!targetScene) {
    if (process === "A") {
      modelValue.processA = {
        name: "",
        processId: "",
        inputArgs: [],
        isEditInputArgs: false,
        metadata: { tag: "A" },
      };
    } else {
      modelValue.processB = {
        name: "",
        processId: "",
        inputArgs: [],
        isEditInputArgs: false,
        metadata: { tag: "B" },
      };
    }
    return;
  }

  const inputArgs: Array<{ key: string; value: any }> = [];
  if (currentProcess.processId) {
    const args = await getInputArgs(currentProcess.processId);
    inputArgs.push(...args);
  }

  const updatedProcess = {
    name: `${targetScene.name}(V${targetScene.version})`,
    processId: targetScene.processId,
    inputArgs: inputArgs,
    region: modelValue.region,
    isEditInputArgs: currentProcess.isEditInputArgs,
    metadata: { tag: process },
  };

  if (process === "A") {
    modelValue.processA = updatedProcess;
  } else {
    modelValue.processB = updatedProcess;
  }
};

// 替换原来的 updateProcessA 和 updateProcessB
const updateProcessA = () => updateProcess("A");
const updateProcessB = () => updateProcess("B");

// 优化 doExperience 函数中的 payload 构建
const buildPayload = (inputArgs: Array<{ key: string; value: any }>) => {
  const payload: Record<string, any> = {};
  inputArgs.forEach((item) => {
    if (item.key && item.value !== undefined) {
      payload[item.key] = item.value;
    }
  });
  return payload;
};

/**
 * 判断是否为快速点击  true：为快速点击 应禁止    false: 否， 可继续下一步操作
 */
function isQuickClick(){
  const now = Date.now();
  if (now - modelValue.lastSend > 2000) { // 2000ms 内只触发一次
    modelValue.lastSend = now;
    return false;
  }else{
    return true;
  }
}

onMounted(async () => {
  //区域
  const regionList = await api.getMetaRegionList();
  modelValue.regionList = regionList as Region[];

  //所有流程
  const sceneListResponse = await sceneApi.getSceneVersionListByName();
  modelValue.sceneList = sceneListResponse.data as Scene[];

  // 初始化 processA 和 processB
  await updateProcessA();
  await updateProcessB();

  //根据全局字典获取获取流程id列表
  const evalExpProd = (await evalSettingApi.listExpProd().then(result => result.data)) as FlowItem[];
  //根据sort字段排序
  evalExpProd.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  //目前图标是从这个页面写死，然后取余获取图标
  modelValue.flowList = evalExpProd.map((item: FlowItem, index: number) => {
    const { icon, ...rest } = item;
    return {
      icon: iconType[index % iconType.length],
      ...rest,
    };
  });
  //流程支持用户自定义
  modelValue.flowList.push({
    icon: "Connection",
    name: "其他产品",
    code: "other",
  });
  modelValue.flowCode = modelValue.flowList[0].code;
});
</script>

<style lang="scss" scoped>
.experience-index {
  display: flex;
  flex-direction: column;
  padding: 8px 15px;
  line-height: 23px;
  .first.flex {
    display: flex;
    align-items: center;
    ::v-deep .el-link__inner {
      white-space: nowrap;
    }
    .flow-code {
      overflow-x: hidden;
    }
    .flow-code:hover {
      overflow-x: auto;
    }
    div + div {
      margin-left: 12px;
    }
  }
  .flexBetween {
    > .flex {
      div + div {
        margin-left: 12px;
      }
    }
  }
  .searchPane {
    height: 100%;
    overflow-y: auto;
    ::v-deep .el-col {
      height: 100%;
      .mark-pane {
        height: 100%;
        padding: 0 6px;
        overflow-y: auto;
      }
    }
  }
  .chatPane {
    height: 100%;
    ::v-deep .el-col {
      height: 100%;
      .chat-pane {
        height: calc(100% - 40px);
        overflow-y: auto;
        padding: 2px 3px;
      }
    }
  }
  .search-row {
    margin: 12px 0;
  }
  ::v-deep {
    .icon-copy {
      color: $primary-color;
      cursor: pointer;
    }
  }
  ::v-deep .el-tabs--top {
    height: 100%;
  }
  ::v-deep .el-tab-pane {
    height: 100%;
  }
  ::v-deep .el-input-group__append {
    background-color: var(--el-color-primary);
    color: #fff;
  }
  .empty-pane,
  .mark-pane,
  .chat-pane {
    height: 100%;
    overflow-y: auto;
  }
  .mark-pane {
    padding: 0 8px;
  }
  .second {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

::v-deep .el-dropdown-menu__item {
  flex-direction: row !important;
  align-items: center !important;
}
</style>
<style lang="scss">
.position-dialog {
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    height: calc(100vh - 72px);
    padding: 0;
  }
  .container {
    height: 100%;
    padding: 0;
  }
}
</style>