<template>
  <div class="mark-item">
    <el-dialog title="全链路" v-model="allChainVisibleMp" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="closeTraceLogsMp" :icon="Back">返回</el-button><Strong></Strong> </template>
      <MarkPosition ref="markPositionRef"></MarkPosition>
    </el-dialog>
    <el-dialog title="全链路" v-model="allChainVisibleEp" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="closeTraceLogsEp" :icon="Back">返回</el-button><Strong></Strong> </template>
      <ExperiencePosition ref="experiencePositionRef"></ExperiencePosition>
    </el-dialog>
    <div class="doc-index">{{ docIndex + 1 }}</div>
    <el-card>
      <el-row>
        <el-col :span="24" class="flex">
          <el-button link type="primary" @click="events.originData">{{ recallInfo.doc.title }}</el-button>
          <div class="flex" style="max-width: 100%; min-width: 0; margin-left: 10px">
            <el-button link type="primary" @click="events.originData" class="url-info" style="flex-grow: 1; min-width: 0">
              <span class="info">{{ recallInfo.doc.url }}</span>
            </el-button>
            <el-icon class="icon-copy" @click="copyText(recallInfo.doc.url)">
              <CopyDocument />
            </el-icon>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <div class="content">
            <span :class="lessContent ? 'less-content' : ''">
              <span style="color: green">content：</span>{{ recallInfo.doc.content }}
              <el-button v-show="!lessContent" link type="primary" @click="lessContent = true">[折叠]</el-button>
            </span>
            <el-button v-show="lessContent" link type="primary" @click="lessContent = false">[更多]</el-button>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="flexBetween mark-item-info">
          <div class="flex">
            <span class="info" style="margin-right: 10px">{{
              dataC.isEmpty(recallInfo.doc.post_ts) || recallInfo.doc.post_ts == 0 ? "暂无时间" : timeC.format(recallInfo.doc.post_ts * 1000, "YYYY-MM-DD hh:mm:ss")
            }}</span>
            &nbsp;
            <span>{{ recallInfo.doc.id }}</span> <el-icon class="icon-copy" @click="copyText(recallInfo.doc.id)"> <CopyDocument /> </el-icon>
          </div>
          <span :title="`${recallInfo.doc._indexName}`" class="info" style="margin-left: 10px">
            来源：{{ recallInfo.doc._indexName }}
          </span>
          <span>
            {{extendFieldsText()}}
          </span>
        </el-col>
      </el-row>
      <el-row v-if="displayScore && !dataC.isEmpty(recallInfo.scoringRanks)" :gutter="8" class="badge-row">
        <el-col :span="24" class="flex">
          <div v-for="item in recallInfo.scoringRanks" class="flex">
            <router-link :to="getAllChainTo(item)">
              <template v-if="item.code != 'ai-process'">
                <el-badge :value="`[${item.index}]`" :offset="[10, 0]" badge-class="badge" @click="events.allChain">
                  <span style="cursor: pointer; color: black">{{ item.name }}<span v-if="item.score === 0 || item.score">：</span></span>
                  <span style="cursor: pointer; color: green">{{ item.score === 0 || item.score ? Number(item.score).toFixed(7) : "" }}</span>
                </el-badge>
                <el-popover v-if="item.props" placement="right" :width="250" trigger="hover">
                  <template #reference>
                    <el-icon class="icon-tip"><Notification /></el-icon>
                  </template>
                  <div v-for="str in getPropsStrList(item.props)">
                    {{ str }}
                  </div>
                </el-popover>
              </template>
              <template v-if="item.code == 'ai-process'">
                <span style="cursor: pointer; color: black" @click="events.allChain">{{ item.name }}：</span>
                <span class="badge" @click="events.allChain">{{ `[${item.index}]` }}</span>
              </template>
            </router-link>
          </div>
        </el-col>
      </el-row>
      <div>
        <el-row v-for="(dim, index1) in dimsForm" :gutter="8">
          <el-col :span="12">
            <div class="flex" style="align-items: center">
              <span class="select-label">{{ dim.name }}<span v-if="dim.required" style="color: red">*</span>：</span>
              <!-- {{dim}} -->
              <template v-if="dim.optType === 1">
                <el-radio-group v-model="dim.value" size="small">
                  <el-radio v-for="(option, index2) in dim.options" :value="option.name">{{ option.name }}</el-radio>
                </el-radio-group>
              </template>
              <template v-else-if="dim.optType === 2">
                <el-checkbox-group v-model="dim.value" size="small">
                  <el-checkbox v-for="(option, index2) in dim.options" :value="option.name">{{ option.name }}</el-checkbox>
                </el-checkbox-group>
              </template>
            </div>
            <div style="padding-left: 20px">
              <template v-for="(option, index2) in dim.options">
                <el-col v-if="!dataC.isEmpty(option.feedbacks) && isShowFeedback(dim, option.name)" :span="12">
                  <span style="font-style: italic; font-size: small">{{ option.name }}问题：</span>
                  <MarkTag
                    v-for="(feedback, index3) in option.feedbacks"
                    v-model="dimsForm[index1].options[index2].feedbacks[index3]"
                    :dict="feedback.content"
                    :optType="dimsForm[index1].options[index2].feedbacks[index3].optType"
                  ></MarkTag>
                </el-col>
              </template>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row v-if="showAscribe" style="margin-top: 5px">
        <el-col :span="24">
          <span>归因分析：</span>
          <MarkTag v-for="(item, index) in modelValue.ascribeList" v-model="modelValue.ascribeList[index]"></MarkTag>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, watchEffect, computed, onMounted, nextTick } from "vue";
import { keys, cloneDeep, assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import { copyText, getText } from "@/utils/helpers";
import useCtx from "@/hooks/useCtx";
import MarkTag from "./MarkTag.vue";
import MarkPosition from "./MarkPosition.vue";
import ExperiencePosition from "./ExperiencePosition.vue";
import { Back } from "@element-plus/icons-vue";
import { MarkDimsInfo, Dimension } from "@/types/mark";

const { $app, $router, proxy } = useCtx();

const emits = defineEmits(["update:modelValue"]);

const props = defineProps<{
  extendFields: string[];
  modelValue: any;
  docIndex: number;
  recallInfo: any;
  dimsInfo: MarkDimsInfo;
  categoryId: string;
  taskId: string;
  strategyId: string;
  strategyList: any[];
  displayScore: boolean;
  chatId: string;
  markRecordId: string;
  targetId:string;
  tags:string;
}>();

const extendFieldsText = () => {
  if (!dataC.isEmpty(props.extendFields)) {
    return props.extendFields
      .map((item) => `${item}: ${props.recallInfo.doc[item] ?? ""}`)
      .join(" ; ");
  }
  return "";
};

/**
 * 合并单个 feedback 的数据
 */
function mergeFeedback(feedbackItem: any, markFeedbacks: any[]) {
  const markFeedback = markFeedbacks.find((mf: any) => mf && mf.name === feedbackItem.name);

  if(markFeedback){
    markFeedback.value = markFeedback.content[0] || ""
  }

  return markFeedback 
    ? { ...feedbackItem, value: markFeedback.value, remark: markFeedback.remark, check: !dataC.isEmpty(markFeedback.value) || !dataC.isEmpty(markFeedback.remark) }
    : { ...feedbackItem };
}

/**
 * 合并单个 option 的数据，包括其 feedbacks
 */
function mergeOption(option: any, markOption: any) {
  if (!markOption) return { ...option };

  const markFeedbacks = markOption.feedbacks || [];
  const mergedFeedbacks = option.feedbacks?.map((feedbackItem: any) => 
    mergeFeedback(feedbackItem, markFeedbacks)
  );

  return { ...option, feedbacks: mergedFeedbacks };
}

/**
 * 合并单个维度的数据
 * dim: 维度信息
 * dimMarkValue: 该维度的标注结果
 */
function mergeDimension(dim: any, dimMarkValue: any) {
  // console.log("mergeDimension,", dim, dimMarkValue);
  
  // 如果标注结果为空或格式不正确，返回原始维度信息
  if (!dimMarkValue || !Array.isArray(dimMarkValue.options) || dimMarkValue.options.length === 0) {
    return { ...dim };
  }

  const mergedOptions = dim.options.map((option: any) => {
    // 找到当前选项的标注结果
    const markOption = dimMarkValue.options.find((item: any) => item.name === option.name);
    return mergeOption(option, markOption);
  });

  if (dim.optType === 1) {
    // 单选模式，value 为字符串
    return {
      ...dim,
      value: dimMarkValue.options?.[0]?.name || '',
      options: mergedOptions,
    };
  } else {
    // 多选模式，value 为字符串数组
    return {
      ...dim,
      value: dimMarkValue.options?.map((item: any) => item.name) || [],
      options: mergedOptions,
    };
  }
}

/**
 * 将 markValue 的选中项合并到 dimsInfo 结构中
 * dimsInfo: 标注维度
 * markValue: 标注结果
 */
function bindMarkValue2DimsInfo(dimsInfo: MarkDimsInfo, markValue: any[]) {
  const dimsInfoCp = cloneDeep(dimsInfo);
  return dimsInfo.map((dim: any) => {
    //找到与当前维度相同的标注结果对象
    const markItem = markValue.find((item: any) => item.name === dim.name);
    return mergeDimension(dim, markItem);
  });
}

const dimsForm = ref(bindMarkValue2DimsInfo(props.dimsInfo, props.modelValue?.dimList || []));

// 添加一个标志来防止循环更新
const isUpdatingFromParent = ref(false);

function emitDimsForm() {
  console.log("update modelValue");

  //拼接dimList - 不要直接修改 props，而是创建新对象
  const newDimList = dimsForm.value.map(processDimension);

  emits("update:modelValue", {
    ...props.modelValue,
    dimList: newDimList,
    dimsForm: cloneDeep(dimsForm.value)
  });
}


/**
 * 处理单个反馈项的数据
 */
function processFeedback(feedback: any) {
  if (!dataC.isEmpty(feedback.value) || !dataC.isEmpty(feedback.remark)) {
    return {
      name: feedback.name,
      // value: feedback.value,
      remark: feedback.remark,
      content: dataC.isEmpty(feedback.value) ? [] : [feedback.value]
    };
  }
  return null;
}

/**
 * 处理单个选项的数据
 */
function processOption(option: any) {
  if (!option) return null;

  const markedfeedback = option.feedbacks?.map(processFeedback).filter(Boolean) || [];
  return {
    name: option.name,
    feedbacks: markedfeedback,
  };
}

/**
 * 处理单个维度的数据
 */
function processDimension(dim: any) {
  if (dim.optType === 1) {
    // 单选模式
    const selectOption = dim.options.find((option: any) => option.name === dim.value);
    if (!selectOption) return { ...dim, value: "", options: [] };

    return {
      name: dim.name,
      options: [processOption(selectOption)].filter(Boolean),
    };
  } else {
    // 多选模式
    const selectedOptions = dim.options.filter((option: any) => (dim.value || []).includes(option.name));
    return {
      name: dim.name,
      options: selectedOptions.map(processOption).filter(Boolean),
    };
  }
}

/**
 * 作用是决定是否展示feedback
 * 根据dim的optType判断optionName是否在value中
 */
function isShowFeedback(dim: any, optionName: string) {
  if (dim.optType === "1") {
    //单选  dim.value为字符串
    return dim.value === optionName;
  } else {
    //多选  dim.value是字符串数组
    return (dim.value || []).includes(optionName);
  }
}

// 监听 props.modelValue?.dimList 的变化，更新 dimsForm
watch(
  () => props.modelValue?.dimList,
  (newDimList) => {
    console.log("markItem watch dimList:", newDimList);
    isUpdatingFromParent.value = true;
    dimsForm.value = bindMarkValue2DimsInfo(props.dimsInfo, newDimList || []);
    nextTick(() => {
      isUpdatingFromParent.value = false;
    });
  },
  { deep: true }
);

// 监听 dimsForm 对象的变化，当变化时更新父组件的值
watch(
  dimsForm,
  (newDimsForm) => {
    // 如果是从父组件更新的，不要触发向上的更新
    if (isUpdatingFromParent.value) {
      console.log("markItem watch dimsForm: skipping update from parent");
      return;
    }
    console.log("markItem watch dimsForm:", newDimsForm);
    // 只发送更新事件，不修改 dimsForm 本身
    emitDimsForm();
  },
  { deep: true }
);

//全链路显示
const allChainVisibleMp = ref(false);
const allChainVisibleEp = ref(false);

function closeTraceLogsMp(){
  allChainVisibleMp.value = false;
  $router.push({ name: `mark-index`});
}

function closeTraceLogsEp(){
  allChainVisibleEp.value = false;
  $router.push({ name: `experience-index`});
}


const lessContent = ref(true);

const showAscribe = computed(() => {
  return !dataC.isEmpty(props.modelValue?.ascribeList);
});

//选中的策略信息
const strategyInfo = computed(() => {
  return dataC.getItemByValue(props.strategyList, props.strategyId, "id");
});

const getPropsStrList = (props: any) => {
  const res = keys(props).map((key) => {
    return `${key}：${JSON.stringify(props[key])}`;
  });
  return res;
};

//获取全链路跳转的query参数
const getAllChainToQuery = () => {
  const oldQuery = $router.currentRoute.value.query;
  let newQuery = {};
  if (oldQuery.markMode == "mission") {
    newQuery = assign(newQuery, {
      markMode: "mission",
      missionId: oldQuery.missionId,
      group: oldQuery.group,
      targetId: oldQuery.targetId,
    });
  }
  newQuery = assign(newQuery, {
    mode: "ceping",
    categoryId: props.categoryId,
    taskId: props.taskId,
    strategyId: props.strategyId,
    chatId: props.chatId,
    searchId: props.recallInfo.doc.id,
    url: props.recallInfo.doc.url,
    markRecordId: props.markRecordId,
    targetId: props.targetId,
    tag:props.tags,
  });
  return newQuery;
};

//获取全链路跳转url地址
const getAllChainTo = (item: any) => {
  const curRouteName = $router.currentRoute.value.name;
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/${curRouteName}/position?${routerQuery.toString()}`;
};

const events = reactive({
  originData: () => {
    window.open(props.recallInfo.doc.url, "_blank");
  },
  allChain: async () => {
    //阻止原生路由跳转事件
    event.preventDefault();
    //修改当前路由
    const curRouteName = $router.currentRoute.value.name;
    await $router.push({ name: `${curRouteName}`, query: getAllChainToQuery() });
    //当归因模式是自研badcase分析，可以跳转全链路
    if (curRouteName == "mark-index") {
      if (strategyInfo.value.ascribeMode == 1) {
        //打开一个全屏dialog
        allChainVisibleMp.value = true;
        //刷新全链路
        nextTick(() => {
          proxy.$refs.markPositionRef.getTraceinfo();
        });
      }
    } else if (curRouteName == "experience-index") {
      //打开一个全屏dialog
      allChainVisibleEp.value = true;
      //刷新全链路
      nextTick(() => {
        proxy.$refs.experiencePositionRef.getTraceinfo();
      });
    }
  },
});
onMounted(() => {
  // 初始化时只在必要时发送更新
  if (!props.modelValue?.dimsForm) {
    emits("update:modelValue", { ...props.modelValue, dimsForm: dimsForm.value });
    console.log("markItem onMounted emits", props.modelValue);
  }
});
//展开正文
const unfoldContent = () => {
  lessContent.value = false;
};
//折叠正文
const foldContent = () => {
  lessContent.value = true;
};
//接口暴露
defineExpose({
  unfoldContent,
  foldContent,
  emitDimsForm,
});
</script>

<style lang="scss" scoped>
.mark-item {
  position: relative;
  margin-bottom: 8px;

  ::v-deep .el-card__body {
    padding: 10px 20px;
  }
  .doc-index {
    position: absolute;
    height: 20px;
    width: 20px;
    left: 8px;
    top: 12px;
    font-family: fantasy;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.15);
  }
  .badge-row {
    padding: 5px;
    background-color: #f8fafd;
    ::v-deep sup.badge,
    .badge {
      cursor: pointer;
      border: none;
      background-color: transparent;
      font-size: 13px;
      color: red;
    }
    > .flex {
      flex-wrap: wrap;
      > .flex {
        width: 250px;
        margin-top: 5px;
      }
    }
  }
  .content {
    display: flex;

    > span.less-content {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    ::v-deep .el-button + .el-button {
      margin-left: 0;
    }
  }

  .icon-copy {
    color: $primary-color;
    cursor: pointer;
  }

  .icon-tip {
    color: $primary-color;
    margin-left: 30px;
  }

  ::v-deep .url-info > span {
    line-height: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .info {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .select-label {
    max-width: 80px;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .mark-item-info {
    font-family: "PingFang SC";
    font-size: 13px;
    color: #606266;
  }
}
</style>
