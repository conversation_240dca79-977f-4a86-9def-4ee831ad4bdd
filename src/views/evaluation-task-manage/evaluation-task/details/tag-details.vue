<template>
  <page-wrapper route-name="evaluation-task::details::tag-details::">
    <div class="evaluation-task-container">
      <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '170px' : '60px' }">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>
            </template>
            <el-descriptions column="4">
              <el-descriptions-item label="任务名称 : " label-class-name="bold">{{ `${routeQuery.name}`
                }}</el-descriptions-item>
              <el-descriptions-item label="分组 : " label-class-name="bold">{{ routeQuery.label }}</el-descriptions-item>
              <el-descriptions-item label="query条数 : " label-class-name="bold">{{ routeQuery.queryCount
                }}</el-descriptions-item>
              <el-descriptions-item label="参与人数 : " label-class-name="bold">{{ routeQuery.userCount
                }}</el-descriptions-item>
              <el-descriptions-item label="数据分配方式 : " label-class-name="bold">{{ routeQuery.typeRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item :span="1" label="测评配置选择 : " label-class-name="bold">{{
                routeQuery.markCategoryIdRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item :span="2" label="活动时间安排 : " label-class-name="bold">{{ routeQuery.timeRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item label="任务描述 : " label-class-name="bold" :span="4">{{
                routeQuery.description
                }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card class="table-card" :style="{
        height:
          activeCollapse == 1 ? 'calc(100vh - 320px)' : 'calc(100vh - 210px)',
      }">
        <div class="el-descriptions">
          <div class="el-descriptions__header">
            <div class="el-descriptions__title">
              <span>{{ "query标注明细" }}</span>
              &nbsp;
              <el-button link type="primary" @click="loadList">
                <el-icon size="18">
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
            <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
          </div>
        </div>

        <div class="evaluation-task-table">
          <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData"
            :transformListData="transformListData" :query="query">
            <template #query>
              <div class="flexBetweenStart">
                <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
                  @reset="events.reset" />
              </div>
            </template>
            <!-- query 字段自定义插槽 -->
            <template #query-slot="{ row }">
              <div class="flex items-center">
                <text-button
                  v-if="row.query"
                  type="primary"
                  @click="events.openQueryDetail(row)"
                  style="font-weight: 550;"
                >
                  {{ row.query }}
                </text-button>
                <span v-else>-</span>
                <el-icon class="icon-copy ml-2" v-if="row.query" @click="copyText(row.query)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
            <!-- URL 字段自定义插槽 -->
            <template #url-slot="{ row }">
              <div class="flex items-center">
                <text-button
                  v-if="row.url"
                  type="primary"
                  @click="events.openUrl(row)"
                  style="font-weight: 550;"
                >
                  {{ row.url }}
                </text-button>
                <span v-else>-</span>
                <el-icon class="icon-copy ml-2" v-if="row.url" @click="copyText(row.url)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
            <!-- 标题字段自定义插槽 -->
            <template #title-slot="{ row }">
              <div class="flex items-center">
                <span>{{ row.title || '-' }}</span>
                <el-icon class="icon-copy ml-2" v-if="row.title" @click="copyText(row.title)" title="复制">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as util from "@/utils/common";
import { keys, assign, cloneDeep } from "lodash";
import { copyText } from "@/utils/helpers";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument } from "@element-plus/icons-vue";
const { $router, proxy, $app } = useCtx();
import * as metaWordDbApi from "@/api/eval-manage";
const mode = $router.currentRoute.value.query.mode;
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const routeQuery = $app.$route.query;
const baseInfo = ref({});
//事件声明
const emit = defineEmits(["preview-data"]);
const activeCollapse = ref([1]);
const query = ref<any>({});
const columns = ref<any[]>([]);

const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "标注人 或 Query 或 策略名称",
    },
  },
  type: {
    type: "select",
    label: "",
    modelValue: "",
    options: [
      { label: "策略结果", value: 0 },
      { label: "全链路结果", value: 1 },
      { label: "大模型结果", value: 2 },
      { label: "query分类", value: 3 },
      { label: "无结果", value: 4 },
    ],
    width: "180px",
    attrs: {
      placeholder: "来源",
    },
  },

  time: {
    type: "datetimerange",
    label: "",
    modelValue: "",
    width: "250px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});
//列配置
const defaultColumns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "sceneProcessName",
    label: "策略名称",
    minWidth: 240,
  },
  // 文本可复制
  {
    prop: "regionName",
    label: "环境",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    minWidth: 350,
    slotName: "query-slot",
  },
  {
    prop: "account",
    label: "标注人",
    minWidth: 120,
  },
  {
    prop: "createdDateRender",
    label: "标注时间",
    minWidth: 180,
  },
  {
    prop: "type",
    label: "来源",
    minWidth: 120,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        0: { type: "info", name: "策略结果" },
        1: { type: "warning", name: "全链路结果" },
        2: { type: "success", name: "大模型结果" },
        3: { type: "danger", name: "query分类" },
        4: { type: "danger", name: "无结果" },
      },
    },
  },
  {
    prop: "docIdxRender",
    label: "结果",
    minWidth: 100,
  },
  {
    prop: "url",
    label: "URL",
    minWidth: 500,
    slotName: "url-slot",
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 200,
    slotName: "title-slot",
  },
]);
//列表查询
const handleParams = (params) => {
  if (params.time) {
    params.startTime = params.time[0]
    params.endTime = params.time[1]
    delete params.time
  }
  return params
}
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = handleParams(data)

    metaWordDbApi.getTablePage({ ...params, column: routeQuery.column, missionId: routeQuery.missionId, markUser: routeQuery.markUser, userType: routeQuery.userType, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName }).then((result) => {
      const arr = util.generateTableColumns(result.content);
      columns.value = defaultColumns.value.concat([...arr]);
      result.content = result.content.map((item) => ({
        ...item,
        ...item.extendFieldMap,
      }));
      resolve(result);
    });
  });
};

//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.docIdxRender = (x.docIdx != undefined && x.type === 0) ? `Top${x.docIdx + 1}` : "-";
    return x;
  });
};
//初始化
onMounted(async () => {
});
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => { },
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  exportExcel: () => {
    metaWordDbApi
      .getTableExport({ column: routeQuery.column, missionId: routeQuery.missionId, markUser: routeQuery.markUser, userType: routeQuery.userType, regionName: routeQuery.regionName, sceneProcessName: routeQuery.sceneProcessName })
      .then((res) =>
        util.downloadFile(
          res,
          `${routeQuery.metaLabel[1]}query标注明细.xlsx`
        )
      );
  },
  // 打开 query 详情
  openQueryDetail: (record: any) => {
    const routeInfo = {
      name: `mark-index`,
      query: {
        markMode: "history",
        markRecordId: record.markRecordId,
      },
    };
    const resolvedRoute = $router.resolve(routeInfo);
    window.open(resolvedRoute.href, "_blank");
  },
  // 打开 URL
  openUrl: (record: any) => {
    window.open(record.url, "_blank");
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};
//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.evaluation-task-container {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;

      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;

      .el-collapse {
        height: 100%;

        .el-collapse-item {
          height: 100%;

          .el-collapse-item__wrap {
            height: 100%;

            .el-collapse-item__content {
              height: 100%;

              .el-descriptions {
                height: 100%;

                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }

    .no-bottom {
      margin-bottom: 0;
    }
  }
}
</style>
<style lang="scss" scoped>
.evaluation-task-table {
  height: calc(100% - 40px);

  ::v-deep {
    .query-wrapper {
      padding: 0 !important;
    }

    .table-wrapper {
      padding: 0 !important;
    }
  }
}

::v-deep(.el-radio-group) {
  margin-bottom: 14px;
}

.total-info,
.inline-block {
  display: inline-block;
}

.name+.name {
  margin-left: 15px;
}

.icon-copy {
  cursor: pointer;
  color: #409eff;
  margin-left: 8px;

  &:hover {
    color: #66b1ff;
  }
}

.flex {
  display: flex;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}
</style>